// Comment Modal Functions

// Open comment modal
function openCommentModal(postId) {
    document.getElementById(`commentModal-${postId}`).classList.remove('hidden');
    document.body.style.overflow = 'hidden';
}

// Close comment modal
function closeCommentModal(postId, event = null) {
    if (event && event.target !== event.currentTarget) {
        return;
    }
    document.getElementById(`commentModal-${postId}`).classList.add('hidden');
    document.body.style.overflow = 'auto';
}

// Open share comment modal
function openShareCommentModal(shareId) {
    document.getElementById(`shareCommentModal-${shareId}`).classList.remove('hidden');
    document.body.style.overflow = 'hidden';
}

// Close share comment modal
function closeShareCommentModal(shareId, event = null) {
    if (event && event.target !== event.currentTarget) {
        return;
    }
    document.getElementById(`shareCommentModal-${shareId}`).classList.add('hidden');
    document.body.style.overflow = 'auto';
}

// Toggle replies visibility in modal
function toggleModalReplies(commentId) {
    const repliesList = document.getElementById(`modal-replies-list-${commentId}`);
    const arrow = document.getElementById(`modal-replies-arrow-${commentId}`);
    const text = document.getElementById(`modal-replies-text-${commentId}`);
    
    if (repliesList && repliesList.classList.contains('hidden')) {
        repliesList.classList.remove('hidden');
        if (arrow) arrow.style.transform = 'rotate(180deg)';
        if (text) text.textContent = 'Hide replies';
    } else if (repliesList) {
        repliesList.classList.add('hidden');
        if (arrow) arrow.style.transform = 'rotate(0deg)';
        const replyCount = repliesList.children.length;
        if (text) text.textContent = `View ${replyCount} ${replyCount === 1 ? 'reply' : 'replies'}`;
    }
}

// Toggle share replies visibility in modal
function toggleModalShareReplies(commentId) {
    const repliesList = document.getElementById(`modal-share-replies-list-${commentId}`);
    const arrow = document.getElementById(`modal-share-replies-arrow-${commentId}`);
    const text = document.getElementById(`modal-share-replies-text-${commentId}`);
    
    if (repliesList && repliesList.classList.contains('hidden')) {
        repliesList.classList.remove('hidden');
        if (arrow) arrow.style.transform = 'rotate(180deg)';
        if (text) text.textContent = 'Hide replies';
    } else if (repliesList) {
        repliesList.classList.add('hidden');
        if (arrow) arrow.style.transform = 'rotate(0deg)';
        const replyCount = repliesList.children.length;
        if (text) text.textContent = `View ${replyCount} ${replyCount === 1 ? 'reply' : 'replies'}`;
    }
}

// Auto-resize textarea
function autoResizeTextarea(textarea) {
    textarea.style.height = 'auto';
    textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';
}

// Override addCommentToDOM to work with modals
const originalAddCommentToDOM = window.addCommentToDOM;
window.addCommentToDOM = function(postId, comment) {
    // Call original function for regular comments list
    if (originalAddCommentToDOM) {
        originalAddCommentToDOM(postId, comment);
    }

    // Also add to modal if it's open
    const modal = document.getElementById(`commentModal-${postId}`);
    if (modal && !modal.classList.contains('hidden')) {
        const modalCommentsList = modal.querySelector(`#comments-list-${postId}`);
        if (modalCommentsList) {
            // Remove "no comments" message if it exists
            const noComments = modalCommentsList.querySelector('.no-comments');
            if (noComments) {
                noComments.remove();
            }

            // Use the existing createCommentHTML function but adapt for modal styling
            const commentHTML = createModalCommentHTML(comment, postId);
            modalCommentsList.insertAdjacentHTML('afterbegin', commentHTML);
        }
    }
};

// Override addReplyToDOM to work with modals
const originalAddReplyToDOM = window.addReplyToDOM;
window.addReplyToDOM = function(parentId, reply) {
    console.log('addReplyToDOM called for parent:', parentId, 'reply:', reply);

    // Call original function for regular comments (non-modal)
    const regularParentComment = document.querySelector(`[data-comment-id="${parentId}"]:not([id^="commentModal-"] [data-comment-id="${parentId}"])`);
    if (regularParentComment && originalAddReplyToDOM) {
        originalAddReplyToDOM(parentId, reply);
    }

    // Handle modal replies
    const modalParentComments = document.querySelectorAll(`[id^="commentModal-"] [data-comment-id="${parentId}"]`);

    modalParentComments.forEach(parentComment => {
        console.log('Processing modal parent comment:', parentComment);

        // Find the modal replies section
        let modalReplyList = document.getElementById(`modal-replies-list-${parentId}`);
        let viewRepliesBtn = document.getElementById(`modal-view-replies-btn-${parentId}`);

        console.log('Modal reply list found:', modalReplyList);
        console.log('View replies button found:', viewRepliesBtn);

        if (!modalReplyList) {
            // Create replies section if it doesn't exist
            const repliesSection = document.createElement('div');
            repliesSection.className = 'mt-3';
            repliesSection.innerHTML = `
                <button onclick="toggleModalReplies(${parentId})"
                        class="flex items-center space-x-2 text-gray-400 hover:text-gray-200 text-sm font-medium transition-colors ml-2"
                        id="modal-view-replies-btn-${parentId}">
                    <svg class="w-4 h-4 transform transition-transform" id="modal-replies-arrow-${parentId}" viewBox="0 0 24 24">
                        <path fill="currentColor" d="M7.41 8.59L12 13.17l4.59-4.58L18 10l-6 6-6-6 1.41-1.41z"/>
                    </svg>
                    <span id="modal-replies-text-${parentId}">
                        View 1 reply
                    </span>
                </button>
                <div class="hidden mt-3 ml-4 space-y-3 border-l-2 border-gray-600 pl-4" id="modal-replies-list-${parentId}">
                </div>
            `;

            parentComment.querySelector('.flex-1').appendChild(repliesSection);
            modalReplyList = document.getElementById(`modal-replies-list-${parentId}`);
            viewRepliesBtn = document.getElementById(`modal-view-replies-btn-${parentId}`);
        }

        // Add the reply using the modal comment structure
        const replyHTML = createModalCommentHTML(reply, reply.commentable_id || reply.post_id);
        modalReplyList.insertAdjacentHTML('beforeend', replyHTML);
        console.log('Reply HTML added to modal');

        // Update the replies count
        const repliesText = document.getElementById(`modal-replies-text-${parentId}`);
        if (repliesText) {
            const currentCount = modalReplyList.children.length;
            repliesText.textContent = `View ${currentCount} ${currentCount === 1 ? 'reply' : 'replies'}`;
            console.log('Updated replies count to:', currentCount);
        }

        // Show replies if hidden
        if (modalReplyList.classList.contains('hidden')) {
            toggleModalReplies(parentId);
            console.log('Toggled modal replies to show');
        }
    });
};

// Override updateCommentCount to work with modals
const originalUpdateCommentCount = window.updateCommentCount;
window.updateCommentCount = function(postId) {
    // Call original function
    if (originalUpdateCommentCount) {
        originalUpdateCommentCount(postId);
    }

    // Also update modal count
    const modalCountElement = document.getElementById(`modal-comments-count-${postId}`);
    if (modalCountElement) {
        const mainCountElement = document.getElementById(`comments-count-${postId}`);
        if (mainCountElement) {
            const countText = mainCountElement.textContent;
            modalCountElement.textContent = countText;
        }
    }
};

// Create modal comment HTML (adapted from existing createCommentHTML)
function createModalCommentHTML(comment, postId) {
    const timeAgo = formatTimeAgo ? formatTimeAgo(comment.created_at) : 'just now';
    const userAvatar = comment.user.avatar
        ? `/storage/${comment.user.avatar}`
        : `https://ui-avatars.com/api/?name=${encodeURIComponent(comment.user.name)}&color=7BC74D&background=EEEEEE`;

    const currentUserAvatar = getCurrentUserAvatar();

    return `
        <div class="comment-item py-4 px-4 hover:bg-gray-700/30 transition-colors duration-150" data-comment-id="${comment.id}">
            <div class="flex space-x-3">
                <a href="/profile/${comment.user.id}" class="flex-shrink-0">
                    <img class="h-10 w-10 rounded-full ring-1 ring-gray-500 shadow-sm"
                         src="${userAvatar}"
                         alt="${comment.user.name}">
                </a>
                <div class="flex-1 min-w-0">
                    <div class="bg-gray-700 rounded-xl p-4 shadow-sm border border-gray-600">
                        <div class="flex items-center space-x-2 mb-2">
                            <a href="/profile/${comment.user.id}" class="font-semibold text-white hover:text-custom-green text-sm hover:underline">
                                ${comment.user.name}
                            </a>
                            <span class="text-xs text-gray-400">${timeAgo}</span>
                            <div class="relative ml-auto">
                                <button onclick="toggleCommentDropdown(${comment.id})" class="text-gray-400 hover:text-gray-200 p-1 rounded-full hover:bg-gray-600">
                                    <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                        <path d="M10 6a2 2 0 110-4 2 2 0 010 4zM10 12a2 2 0 110-4 2 2 0 010 4zM10 18a2 2 0 110-4 2 2 0 010 4z"/>
                                    </svg>
                                </button>
                                <div id="comment-dropdown-${comment.id}" class="hidden absolute right-0 top-8 w-36 bg-gray-600 rounded-lg shadow-lg border border-gray-500 py-1 z-10">
                                    <button onclick="editComment(${comment.id}); hideCommentDropdown(${comment.id})"
                                            class="w-full text-left px-4 py-2 text-sm text-gray-200 hover:bg-gray-500 flex items-center space-x-2 transition-colors">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z" />
                                        </svg>
                                        <span>Edit</span>
                                    </button>
                                    <button onclick="deleteComment(${comment.id}); hideCommentDropdown(${comment.id})"
                                            class="w-full text-left px-4 py-2 text-sm text-red-400 hover:bg-red-900/20 flex items-center space-x-2 transition-colors">
                                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                                        </svg>
                                        <span>Delete</span>
                                    </button>
                                </div>
                            </div>
                        </div>

                        <div class="comment-content">
                            <p class="text-gray-200 text-sm leading-relaxed">${comment.content.replace(/\n/g, '<br>')}</p>
                        </div>

                        <!-- Edit form (hidden by default) -->
                        <div class="comment-edit-form hidden mt-3">
                            <form class="edit-comment-form" data-comment-id="${comment.id}">
                                <input type="hidden" name="_token" value="${document.querySelector('meta[name="csrf-token"]').getAttribute('content')}">
                                <input type="hidden" name="_method" value="PUT">
                                <textarea name="content" rows="2"
                                          class="w-full bg-gray-600 text-white border border-gray-500 rounded-lg shadow-sm focus:ring-custom-green focus:border-custom-green resize-none text-sm p-3">${comment.content}</textarea>
                                <div class="mt-2 flex justify-end space-x-2">
                                    <button type="button" onclick="cancelEditComment(${comment.id})"
                                            class="px-3 py-1.5 text-sm text-gray-400 hover:text-gray-200 rounded-md hover:bg-gray-600 transition-colors">Cancel</button>
                                    <button type="submit"
                                            class="px-3 py-1.5 text-sm bg-custom-green text-white rounded-md hover:bg-green-600 transition-colors">Save</button>
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Comment Actions -->
                    <div class="comment-actions flex items-center space-x-4 mt-2 ml-2 text-sm">
                        <button onclick="toggleCommentLike(${comment.id})"
                                class="flex items-center space-x-1 text-gray-400 hover:text-red-400 transition-colors group ${comment.is_liked_by_user ? 'text-red-400' : ''}"
                                id="comment-like-btn-${comment.id}">
                            <svg class="w-4 h-4 ${comment.is_liked_by_user ? 'text-red-400 fill-current' : ''} group-hover:scale-110 transition-transform duration-200"
                                 fill="${comment.is_liked_by_user ? 'currentColor' : 'none'}"
                                 stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4.318 6.318a4.5 4.5 0 000 6.364L12 20.364l7.682-7.682a4.5 4.5 0 00-6.364-6.364L12 7.636l-1.318-1.318a4.5 4.5 0 00-6.364 0z" />
                            </svg>
                            <span id="comment-like-count-${comment.id}" class="font-medium">${comment.likes_count || 0}</span>
                        </button>

                        <button onclick="showReplyForm(${comment.id})" class="flex items-center space-x-1 text-gray-400 hover:text-blue-400 transition-colors group">
                            <svg class="w-4 h-4 group-hover:scale-110 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6" />
                            </svg>
                            <span class="font-medium">Reply</span>
                        </button>
                    </div>

                    <!-- Reply Form (hidden by default) -->
                    <div class="reply-form hidden mt-3 ml-4" id="reply-form-${comment.id}">
                        <form class="comment-form" data-post-id="${postId}" data-parent-id="${comment.id}">
                            <input type="hidden" name="_token" value="${document.querySelector('meta[name="csrf-token"]').getAttribute('content')}">
                            <div class="flex space-x-3">
                                <div class="flex-shrink-0">
                                    <img class="h-8 w-8 rounded-full ring-1 ring-gray-500 shadow-sm"
                                         src="${currentUserAvatar}"
                                         alt="Your avatar">
                                </div>
                                <div class="flex-1 min-w-0">
                                    <div class="relative">
                                        <textarea name="content" rows="1"
                                                  placeholder="Write a reply..."
                                                  class="w-full px-3 py-2 bg-gray-600 text-white border border-gray-500 rounded-lg shadow-sm focus:ring-2 focus:ring-custom-green/20 focus:border-custom-green resize-none text-sm hover:bg-gray-500 transition-colors duration-200"
                                                  required></textarea>
                                    </div>
                                    <div class="mt-2 flex justify-end space-x-2">
                                        <button type="button" onclick="hideReplyForm(${comment.id})"
                                                class="px-3 py-1.5 text-sm text-gray-400 hover:text-gray-200 rounded-md hover:bg-gray-600 transition-colors">Cancel</button>
                                        <button type="submit"
                                                class="px-3 py-1.5 bg-custom-green text-white text-sm font-medium rounded-md hover:bg-custom-second-darkest shadow-sm transition-all duration-200 hover:shadow">
                                            Reply
                                        </button>
                                    </div>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    `;
}



// Submit comment in modal
async function submitModalComment(form) {
    const formData = new FormData(form);
    const postId = form.dataset.postId;
    const parentId = form.dataset.parentId;

    if (parentId) {
        formData.append('parent_id', parentId);
    }

    try {
        const csrfToken = document.querySelector('meta[name="csrf-token"]')?.getAttribute('content');

        const response = await fetch(`/posts/${postId}/comments`, {
            method: 'POST',
            body: formData,
            headers: {
                'X-Requested-With': 'XMLHttpRequest',
                'X-CSRF-TOKEN': csrfToken
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP ${response.status}`);
        }

        const data = await response.json();

        if (data.success) {
            // Clear the form
            form.reset();

            // Hide submit button
            const submitBtn = form.querySelector('[id^="modal-comment-submit-btn-"]');
            if (submitBtn) {
                submitBtn.style.opacity = '0';
            }

            // Hide reply form if it was a reply
            if (parentId) {
                hideReplyForm(parentId);
                // Add reply to the parent comment's replies section
                addReplyToDOM(parentId, data.comment);
            } else {
                // Add new main comment to the comments list
                addCommentToDOM(postId, data.comment);
            }

            // Update comment count
            updateCommentCount(postId);
        } else {
            alert('Error posting comment: ' + (data.message || 'Unknown error'));
        }
    } catch (error) {
        console.error('Error submitting modal comment:', error);
        alert('Error posting comment. Please try again.');
    }
}

// Helper function to get current user avatar
function getCurrentUserAvatar() {
    const userAvatarElement = document.querySelector('meta[name="user-avatar"]');
    if (userAvatarElement) {
        return userAvatarElement.getAttribute('content');
    }

    // Fallback: try to get from any existing user avatar in the page
    const existingAvatar = document.querySelector('img[alt*="' + (document.querySelector('meta[name="user-name"]')?.getAttribute('content') || '') + '"]');
    if (existingAvatar) {
        return existingAvatar.src;
    }

    // Final fallback
    const userName = document.querySelector('meta[name="user-name"]')?.getAttribute('content') || 'User';
    return `https://ui-avatars.com/api/?name=${encodeURIComponent(userName)}&color=7BC74D&background=EEEEEE`;
}

// Toggle comment dropdown
function toggleCommentDropdown(commentId) {
    const dropdown = document.getElementById(`comment-dropdown-${commentId}`);
    if (dropdown) {
        dropdown.classList.toggle('hidden');
    }

    // Close other dropdowns
    document.querySelectorAll('[id^="comment-dropdown-"]').forEach(otherDropdown => {
        if (otherDropdown.id !== `comment-dropdown-${commentId}`) {
            otherDropdown.classList.add('hidden');
        }
    });
}

// Hide comment dropdown
function hideCommentDropdown(commentId) {
    const dropdown = document.getElementById(`comment-dropdown-${commentId}`);
    if (dropdown) {
        dropdown.classList.add('hidden');
    }
}

// Ensure edit and delete functions work in modal
window.editComment = function(commentId) {
    // Try to find elements in both modal and main feed
    const commentElements = document.querySelectorAll(`[data-comment-id="${commentId}"]`);

    commentElements.forEach(commentElement => {
        const commentContent = commentElement.querySelector('.comment-content');
        const editForm = commentElement.querySelector('.comment-edit-form');

        if (commentContent && editForm) {
            commentContent.classList.add('hidden');
            editForm.classList.remove('hidden');
            const textarea = editForm.querySelector('textarea');

            if (textarea) {
                textarea.focus();
                // Auto-resize if in modal
                if (commentElement.closest('[id^="commentModal-"]')) {
                    autoResizeTextarea(textarea);
                }
            }
        }
    });
};

window.cancelEditComment = function(commentId) {
    // Try to find elements in both modal and main feed
    const commentElements = document.querySelectorAll(`[data-comment-id="${commentId}"]`);

    commentElements.forEach(commentElement => {
        const commentContent = commentElement.querySelector('.comment-content');
        const editForm = commentElement.querySelector('.comment-edit-form');

        if (commentContent && editForm) {
            commentContent.classList.remove('hidden');
            editForm.classList.add('hidden');
        }
    });
};

window.deleteComment = async function(commentId) {
    if (!confirm('Are you sure you want to delete this comment?')) {
        return;
    }

    try {
        const response = await fetch(`/comments/${commentId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'X-Requested-With': 'XMLHttpRequest'
            }
        });

        const data = await response.json();

        if (data.success) {
            // Remove comment from DOM (both modal and main feed)
            const commentElements = document.querySelectorAll(`[data-comment-id="${commentId}"]`);
            commentElements.forEach(element => {
                element.remove();
            });

            // Update comment count if needed
            // This would require knowing which post the comment belongs to
        } else {
            alert('Error deleting comment: ' + (data.message || 'Unknown error'));
        }
    } catch (error) {
        console.error('Error deleting comment:', error);
        alert('Error deleting comment. Please try again.');
    }
};

// Hide reply form
function hideReplyForm(commentId) {
    const replyForm = document.getElementById(`reply-form-${commentId}`);
    if (replyForm) {
        replyForm.classList.add('hidden');
        // Clear the textarea
        const textarea = replyForm.querySelector('textarea[name="content"]');
        if (textarea) {
            textarea.value = '';
        }
    }
}

// Override toggleCommentLike for modal compatibility
const originalToggleCommentLike = window.toggleCommentLike;
window.toggleCommentLike = async function(commentId) {
    try {
        const response = await fetch(`/comments/${commentId}/like`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'X-CSRF-TOKEN': document.querySelector('meta[name="csrf-token"]').getAttribute('content'),
                'X-Requested-With': 'XMLHttpRequest'
            }
        });

        const data = await response.json();

        if (data.success) {
            // Update all instances of this comment (both in modal and main feed)
            const likeBtns = document.querySelectorAll(`#comment-like-btn-${commentId}`);
            const likeCounts = document.querySelectorAll(`#comment-like-count-${commentId}`);

            likeBtns.forEach(likeBtn => {
                const heartIcon = likeBtn.querySelector('svg');

                // Add animation effect
                likeBtn.style.transform = 'scale(1.1)';
                setTimeout(() => {
                    likeBtn.style.transform = 'scale(1)';
                }, 150);

                if (data.liked) {
                    likeBtn.classList.add('text-red-600');
                    likeBtn.classList.remove('text-gray-500');
                    if (heartIcon) {
                        heartIcon.classList.add('text-red-600', 'fill-current');
                        heartIcon.setAttribute('fill', 'currentColor');
                        // Add a subtle bounce animation
                        heartIcon.style.animation = 'heartBeat 0.6s ease-in-out';
                        setTimeout(() => {
                            heartIcon.style.animation = '';
                        }, 600);
                    }
                } else {
                    likeBtn.classList.remove('text-red-600');
                    likeBtn.classList.add('text-gray-500');
                    if (heartIcon) {
                        heartIcon.classList.remove('text-red-600', 'fill-current');
                        heartIcon.setAttribute('fill', 'none');
                    }
                }
            });

            // Update like counts
            likeCounts.forEach(likeCount => {
                likeCount.textContent = data.likes_count;
            });
        }
    } catch (error) {
        console.error('Error toggling comment like:', error);
    }
};

// Debug function to test element existence
window.debugCommentElements = function(commentId) {
    console.log('=== Debug Comment Elements ===');
    console.log('Comment ID:', commentId);
    console.log('Reply forms found:', document.querySelectorAll(`#reply-form-${commentId}`));
    console.log('Comment elements found:', document.querySelectorAll(`[data-comment-id="${commentId}"]`));
    console.log('Comment content found:', document.querySelectorAll(`[data-comment-id="${commentId}"] .comment-content`));
    console.log('Edit forms found:', document.querySelectorAll(`[data-comment-id="${commentId}"] .comment-edit-form`));
    console.log('===============================');
};

// Override showReplyForm for modal compatibility
const originalShowReplyForm = window.showReplyForm;
window.showReplyForm = function(commentId) {
    console.log('showReplyForm called for:', commentId);

    // Debug elements
    window.debugCommentElements(commentId);

    // Try to find reply forms in both modal and main feed
    const replyForms = document.querySelectorAll(`#reply-form-${commentId}`);
    console.log('Found', replyForms.length, 'reply forms');

    replyForms.forEach((replyForm, index) => {
        console.log(`Processing reply form ${index}:`, replyForm);
        if (replyForm) {
            replyForm.classList.remove('hidden');
            const textarea = replyForm.querySelector('textarea');
            console.log('Textarea found:', textarea);

            if (textarea) {
                textarea.focus();
                // Auto-resize if in modal
                if (replyForm.closest('[id^="commentModal-"]')) {
                    autoResizeTextarea(textarea);
                }
            }
        }
    });
};

// Initialize modal functionality
document.addEventListener('DOMContentLoaded', function() {
    // Auto-resize textareas in modals and show/hide submit button
    document.addEventListener('input', function(e) {
        if (e.target.tagName === 'TEXTAREA' && e.target.closest('[id^="commentModal-"], [id^="shareCommentModal-"]')) {
            autoResizeTextarea(e.target);

            // Show/hide submit button based on content
            const form = e.target.closest('form');
            if (form) {
                const postId = form.dataset.postId;
                const shareId = form.dataset.shareId;
                let submitBtnId;

                if (postId) {
                    submitBtnId = `modal-comment-submit-btn-${postId}`;
                } else if (shareId) {
                    submitBtnId = `modal-share-comment-submit-btn-${shareId}`;
                }

                const submitBtn = document.getElementById(submitBtnId);
                if (submitBtn) {
                    if (e.target.value.trim()) {
                        submitBtn.style.opacity = '1';
                    } else {
                        submitBtn.style.opacity = '0';
                    }
                }
            }
        }
    });

    // Handle Enter key for comment submission in modals
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Enter' && !e.shiftKey && e.target.tagName === 'TEXTAREA') {
            const form = e.target.closest('.comment-form, .share-comment-form');
            if (form && e.target.value.trim() && form.closest('[id^="commentModal-"], [id^="shareCommentModal-"]')) {
                e.preventDefault();
                // Directly call the submit function instead of dispatching event
                if (form.classList.contains('comment-form')) {
                    submitModalComment(form);
                } else if (form.classList.contains('share-comment-form')) {
                    submitInternalShare(form);
                }
            }
        }
    });

    // Handle form submissions in modals
    document.addEventListener('submit', function(e) {
        if (e.target.classList.contains('comment-form') && e.target.closest('[id^="commentModal-"]')) {
            e.preventDefault();
            submitModalComment(e.target);
        }
        if (e.target.classList.contains('edit-comment-form') && e.target.closest('[id^="commentModal-"]')) {
            e.preventDefault();
            submitCommentEdit(e.target);
        }
        if (e.target.classList.contains('share-comment-form') && e.target.closest('[id^="shareCommentModal-"]')) {
            e.preventDefault();
            submitInternalShare(e.target);
        }
    });

    // Close dropdowns when clicking outside
    document.addEventListener('click', function(e) {
        if (!e.target.closest('[id^="comment-dropdown-"]') && !e.target.closest('button[onclick*="toggleCommentDropdown"]')) {
            document.querySelectorAll('[id^="comment-dropdown-"]').forEach(dropdown => {
                dropdown.classList.add('hidden');
            });
        }
    });
});
