// Comment Modal Functions

// Open comment modal
function openCommentModal(postId) {
    document.getElementById(`commentModal-${postId}`).classList.remove('hidden');
    document.body.style.overflow = 'hidden';
}

// Close comment modal
function closeCommentModal(postId, event = null) {
    if (event && event.target !== event.currentTarget) {
        return;
    }
    document.getElementById(`commentModal-${postId}`).classList.add('hidden');
    document.body.style.overflow = 'auto';
}

// Open share comment modal
function openShareCommentModal(shareId) {
    document.getElementById(`shareCommentModal-${shareId}`).classList.remove('hidden');
    document.body.style.overflow = 'hidden';
}

// Close share comment modal
function closeShareCommentModal(shareId, event = null) {
    if (event && event.target !== event.currentTarget) {
        return;
    }
    document.getElementById(`shareCommentModal-${shareId}`).classList.add('hidden');
    document.body.style.overflow = 'auto';
}

// Toggle replies visibility in modal
function toggleModalReplies(commentId) {
    const repliesList = document.getElementById(`modal-replies-list-${commentId}`);
    const arrow = document.getElementById(`modal-replies-arrow-${commentId}`);
    const text = document.getElementById(`modal-replies-text-${commentId}`);
    
    if (repliesList && repliesList.classList.contains('hidden')) {
        repliesList.classList.remove('hidden');
        if (arrow) arrow.style.transform = 'rotate(180deg)';
        if (text) text.textContent = 'Hide replies';
    } else if (repliesList) {
        repliesList.classList.add('hidden');
        if (arrow) arrow.style.transform = 'rotate(0deg)';
        const replyCount = repliesList.children.length;
        if (text) text.textContent = `View ${replyCount} ${replyCount === 1 ? 'reply' : 'replies'}`;
    }
}

// Toggle share replies visibility in modal
function toggleModalShareReplies(commentId) {
    const repliesList = document.getElementById(`modal-share-replies-list-${commentId}`);
    const arrow = document.getElementById(`modal-share-replies-arrow-${commentId}`);
    const text = document.getElementById(`modal-share-replies-text-${commentId}`);
    
    if (repliesList && repliesList.classList.contains('hidden')) {
        repliesList.classList.remove('hidden');
        if (arrow) arrow.style.transform = 'rotate(180deg)';
        if (text) text.textContent = 'Hide replies';
    } else if (repliesList) {
        repliesList.classList.add('hidden');
        if (arrow) arrow.style.transform = 'rotate(0deg)';
        const replyCount = repliesList.children.length;
        if (text) text.textContent = `View ${replyCount} ${replyCount === 1 ? 'reply' : 'replies'}`;
    }
}

// Auto-resize textarea
function autoResizeTextarea(textarea) {
    textarea.style.height = 'auto';
    textarea.style.height = Math.min(textarea.scrollHeight, 120) + 'px';
}

// Override addCommentToDOM to work with modals
const originalAddCommentToDOM = window.addCommentToDOM;
window.addCommentToDOM = function(postId, comment) {
    // Call original function for regular comments list
    if (originalAddCommentToDOM) {
        originalAddCommentToDOM(postId, comment);
    }

    // Also add to modal if it's open
    const modal = document.getElementById(`commentModal-${postId}`);
    if (modal && !modal.classList.contains('hidden')) {
        const modalCommentsList = modal.querySelector(`#comments-list-${postId}`);
        if (modalCommentsList) {
            // Remove "no comments" message if it exists
            const noComments = modalCommentsList.querySelector('.no-comments');
            if (noComments) {
                noComments.remove();
            }

            // Add new comment to modal
            const commentHTML = createModalCommentHTML(comment);
            modalCommentsList.insertAdjacentHTML('afterbegin', commentHTML);
        }
    }
};

// Override addReplyToDOM to work with modals
const originalAddReplyToDOM = window.addReplyToDOM;
window.addReplyToDOM = function(parentId, reply) {
    // Call original function for regular comments
    if (originalAddReplyToDOM) {
        originalAddReplyToDOM(parentId, reply);
    }

    // Also add to modal if it's open
    const modalReplyList = document.getElementById(`modal-replies-list-${parentId}`);
    if (modalReplyList) {
        const replyHTML = createModalReplyHTML(reply);
        modalReplyList.insertAdjacentHTML('beforeend', replyHTML);

        // Show replies if hidden
        if (modalReplyList.classList.contains('hidden')) {
            toggleModalReplies(parentId);
        }
    }
};

// Override updateCommentCount to work with modals
const originalUpdateCommentCount = window.updateCommentCount;
window.updateCommentCount = function(postId) {
    // Call original function
    if (originalUpdateCommentCount) {
        originalUpdateCommentCount(postId);
    }

    // Also update modal count
    const modalCountElement = document.getElementById(`modal-comments-count-${postId}`);
    if (modalCountElement) {
        const mainCountElement = document.getElementById(`comments-count-${postId}`);
        if (mainCountElement) {
            const countText = mainCountElement.textContent;
            modalCountElement.textContent = countText;
        }
    }
};

// Create modal comment HTML (simplified version)
function createModalCommentHTML(comment) {
    const userAvatar = comment.user.avatar
        ? `/storage/${comment.user.avatar}`
        : `https://ui-avatars.com/api/?name=${encodeURIComponent(comment.user.name)}&color=7BC74D&background=EEEEEE`;

    const timeAgo = formatTimeAgo ? formatTimeAgo(comment.created_at) : 'just now';

    return `
        <div class="modal-comment-item py-4 px-4 hover:bg-gray-700/30 transition-colors duration-150" data-comment-id="${comment.id}">
            <div class="flex space-x-3">
                <a href="/profile/${comment.user.id}" class="flex-shrink-0">
                    <img class="h-8 w-8 rounded-full" src="${userAvatar}" alt="${comment.user.name}">
                </a>
                <div class="flex-1 min-w-0">
                    <div class="bg-gray-700 rounded-2xl px-4 py-3">
                        <div class="flex items-center space-x-2 mb-1">
                            <a href="/profile/${comment.user.id}" class="font-semibold text-white hover:text-custom-green text-sm hover:underline">
                                ${comment.user.name}
                            </a>
                        </div>
                        <div class="comment-content">
                            <p class="text-gray-200 text-sm leading-relaxed">${comment.content.replace(/\n/g, '<br>')}</p>
                        </div>
                    </div>
                    <div class="flex items-center space-x-4 mt-2 text-xs text-gray-400">
                        <span>${timeAgo}</span>
                        <button onclick="toggleCommentLike(${comment.id})" class="hover:text-red-400 transition-colors font-medium" id="comment-like-btn-${comment.id}">
                            Like
                        </button>
                        <button onclick="showReplyForm(${comment.id})" class="hover:text-blue-400 transition-colors font-medium">
                            Reply
                        </button>
                    </div>
                    <div class="reply-form hidden mt-3" id="reply-form-${comment.id}">
                        <form class="comment-form" data-post-id="${comment.post_id}" data-parent-id="${comment.id}">
                            <input type="hidden" name="_token" value="${document.querySelector('meta[name="csrf-token"]').getAttribute('content')}">
                            <div class="flex space-x-3">
                                <img class="h-6 w-6 rounded-full" src="${userAvatar}" alt="Your avatar">
                                <div class="flex-1">
                                    <textarea name="content" rows="1" placeholder="Reply to ${comment.user.name}" class="w-full bg-gray-600 text-white border border-gray-500 rounded-full px-3 py-2 focus:outline-none focus:ring-2 focus:ring-custom-green focus:border-transparent resize-none text-sm" style="min-height: 32px;"></textarea>
                                </div>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    `;
}

// Create modal reply HTML (simplified version)
function createModalReplyHTML(reply) {
    const userAvatar = reply.user.avatar
        ? `/storage/${reply.user.avatar}`
        : `https://ui-avatars.com/api/?name=${encodeURIComponent(reply.user.name)}&color=7BC74D&background=EEEEEE`;

    const timeAgo = formatTimeAgo ? formatTimeAgo(reply.created_at) : 'just now';

    return `
        <div class="modal-reply-item py-2" data-reply-id="${reply.id}">
            <div class="flex space-x-2">
                <a href="/profile/${reply.user.id}" class="flex-shrink-0">
                    <img class="h-6 w-6 rounded-full" src="${userAvatar}" alt="${reply.user.name}">
                </a>
                <div class="flex-1 min-w-0">
                    <div class="bg-gray-700 rounded-xl px-3 py-2">
                        <div class="flex items-center space-x-2 mb-1">
                            <a href="/profile/${reply.user.id}" class="font-semibold text-white hover:text-custom-green text-xs hover:underline">
                                ${reply.user.name}
                            </a>
                        </div>
                        <p class="text-gray-200 text-xs leading-relaxed">${reply.content.replace(/\n/g, '<br>')}</p>
                    </div>
                    <div class="flex items-center space-x-3 mt-1 text-xs text-gray-400">
                        <span>${timeAgo}</span>
                        <button onclick="toggleCommentLike(${reply.id})" class="hover:text-red-400 transition-colors font-medium" id="comment-like-btn-${reply.id}">
                            Like
                        </button>
                        <button onclick="showReplyForm(${reply.parent_id})" class="hover:text-blue-400 transition-colors font-medium">
                            Reply
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;
}

// Initialize modal functionality
document.addEventListener('DOMContentLoaded', function() {
    // Auto-resize textareas in modals
    document.addEventListener('input', function(e) {
        if (e.target.tagName === 'TEXTAREA' && e.target.closest('[id^="commentModal-"], [id^="shareCommentModal-"]')) {
            autoResizeTextarea(e.target);
        }
    });

    // Handle Enter key for comment submission in modals
    document.addEventListener('keydown', function(e) {
        if (e.key === 'Enter' && !e.shiftKey && e.target.tagName === 'TEXTAREA') {
            const form = e.target.closest('.comment-form, .share-comment-form');
            if (form && e.target.value.trim() && form.closest('[id^="commentModal-"], [id^="shareCommentModal-"]')) {
                e.preventDefault();
                form.dispatchEvent(new Event('submit', { bubbles: true }));
            }
        }
    });
});
